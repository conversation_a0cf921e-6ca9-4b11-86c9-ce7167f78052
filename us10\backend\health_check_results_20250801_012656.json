{"timestamp": "2025-08-01T01:26:56.208239", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:26:19.628895"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:26:22.188323"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:24.849903"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:26.883652"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:29.066759"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:31.255182"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:33.311351"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:35.570225"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:37.620364"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:39.763709"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:41.817327"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:26:43.879351"}, {"test_name": "User Registration", "success": false, "message": "Validation failed", "details": null, "timestamp": "2025-08-01T01:26:46.517366"}, {"test_name": "User Login", "success": false, "message": "Login successful", "details": null, "timestamp": "2025-08-01T01:26:50.070883"}, {"test_name": "Protected Endpoints", "success": false, "message": "No auth token available", "details": null, "timestamp": "2025-08-01T01:26:50.584695"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 0 resumes, 0 job descriptions", "details": null, "timestamp": "2025-08-01T01:26:53.133939"}, {"test_name": "AI Services", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:26:55.697840"}]}