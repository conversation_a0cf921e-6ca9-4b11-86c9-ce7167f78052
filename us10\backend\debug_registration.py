#!/usr/bin/env python3
"""
Debug registration test
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_registration():
    """Debug registration test"""
    print("🔍 Testing registration...")
    
    # Try with a unique email
    unique_email = f"healthcheck_{int(time.time())}@drresume.com"
    
    registration_data = {
        'first_name': 'Health',
        'last_name': 'Check',
        'email': unique_email,
        'password': 'HealthCheck123!',
        'confirm_password': 'HealthCheck123!'
    }
    
    print(f"Registration data: {registration_data}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/register",
            json=registration_data,
            timeout=10
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")
        except:
            print(f"Response text: {response.text}")
            
        if response.status_code in [200, 201]:
            data = response.json()
            if data.get('success'):
                print("✅ Registration successful!")
                return True
            else:
                print(f"❌ Registration failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_registration()
