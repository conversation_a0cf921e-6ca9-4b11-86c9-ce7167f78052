#!/usr/bin/env python3
"""
Debug AI service test
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_ai_service():
    """Debug AI service test"""
    print("🔍 Testing AI service...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/test-suggestions", timeout=15)
        
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")
        except:
            print(f"Response text: {response.text}")
            
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ AI service working!")
                return True
            else:
                print(f"❌ AI service failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_ai_service()
