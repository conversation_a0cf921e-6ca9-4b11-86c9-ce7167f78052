{"timestamp": "2025-08-01T01:42:00.440086", "overall_success": true, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:41:15.156017"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:41:17.731323"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:20.290731"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:22.335697"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:24.436751"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:26.485695"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:28.583330"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:30.660605"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:32.706780"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:34.760088"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:36.822208"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:41:38.886769"}, {"test_name": "User Registration", "success": true, "message": "Registration successful", "details": null, "timestamp": "2025-08-01T01:41:42.720786"}, {"test_name": "User Login", "success": true, "message": "Login successful, JWT token received", "details": null, "timestamp": "2025-08-01T01:41:46.149730"}, {"test_name": "Protected API - Account Info", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:41:48.713098"}, {"test_name": "Protected API - Scan History", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:41:50.786325"}, {"test_name": "Protected API - Available Suggestions", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:41:52.865512"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 1 resumes, 1 job descriptions", "details": null, "timestamp": "2025-08-01T01:41:55.437441"}, {"test_name": "AI Services", "success": true, "message": "AI suggestion service working", "details": null, "timestamp": "2025-08-01T01:41:59.921730"}]}