{"timestamp": "2025-08-01T01:39:36.161368", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:38:49.186654"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:38:51.748624"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:38:54.320429"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:38:56.402297"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:38:58.466707"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:00.530765"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:02.616237"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:04.696682"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:06.745192"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:08.791097"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:10.883524"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:39:12.936814"}, {"test_name": "User Registration", "success": false, "message": "Registration validation failed: Validation failed", "details": null, "timestamp": "2025-08-01T01:39:17.680542"}, {"test_name": "User Login", "success": true, "message": "Login successful, JWT token received", "details": null, "timestamp": "2025-08-01T01:39:21.838061"}, {"test_name": "Protected API - Account Info", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:39:24.418547"}, {"test_name": "Protected API - Scan History", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:39:26.487169"}, {"test_name": "Protected API - Available Suggestions", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:39:28.552623"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 1 resumes, 1 job descriptions", "details": null, "timestamp": "2025-08-01T01:39:31.112446"}, {"test_name": "AI Services", "success": true, "message": "AI suggestion service working", "details": null, "timestamp": "2025-08-01T01:39:35.644659"}]}