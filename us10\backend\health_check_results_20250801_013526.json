{"timestamp": "2025-08-01T01:35:26.495322", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:34:42.709832"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:34:45.271089"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:47.856677"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:49.900324"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:51.955814"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:54.010360"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:56.093759"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:34:58.125543"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:35:00.210604"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:35:02.250394"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:35:04.337978"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:35:06.390380"}, {"test_name": "User Registration", "success": false, "message": "Validation failed", "details": null, "timestamp": "2025-08-01T01:35:08.939894"}, {"test_name": "User Login", "success": true, "message": "Login successful, JWT token received", "details": null, "timestamp": "2025-08-01T01:35:12.615284"}, {"test_name": "Protected API - Account Info", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:35:15.327718"}, {"test_name": "Protected API - Scan History", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:35:17.437803"}, {"test_name": "Protected API - Available Suggestions", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:35:19.496845"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 1 resumes, 1 job descriptions", "details": null, "timestamp": "2025-08-01T01:35:22.059843"}, {"test_name": "AI Services", "success": true, "message": "AI suggestion service working", "details": null, "timestamp": "2025-08-01T01:35:25.961346"}]}