{"timestamp": "2025-08-01T01:24:23.318022", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:23:48.210370"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:23:50.781163"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:23:53.350435"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:23:55.392413"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:23:57.462262"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:23:59.524759"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:01.588381"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:03.668582"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:05.720728"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:07.771332"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:09.827624"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:24:11.890916"}, {"test_name": "User Registration", "success": false, "message": "HTTP 400", "details": null, "timestamp": "2025-08-01T01:24:14.456347"}, {"test_name": "User Login", "success": false, "message": "HTTP 401", "details": null, "timestamp": "2025-08-01T01:24:16.999260"}, {"test_name": "Protected Endpoints", "success": false, "message": "No auth token available", "details": null, "timestamp": "2025-08-01T01:24:17.520956"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 0 resumes, 0 job descriptions", "details": null, "timestamp": "2025-08-01T01:24:20.124141"}, {"test_name": "AI Services", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:24:22.675954"}]}