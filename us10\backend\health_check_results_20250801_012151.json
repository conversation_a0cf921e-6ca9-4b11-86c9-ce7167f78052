{"timestamp": "2025-08-01T01:21:51.153837", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:21:15.389498"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:21:17.975799"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:20.545897"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:22.629856"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:24.676463"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:26.842692"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:28.932206"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:31.014376"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:33.095275"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:35.153333"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:37.278473"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:21:39.432095"}, {"test_name": "User Registration", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:21:41.991231"}, {"test_name": "User Login", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:21:44.587482"}, {"test_name": "Protected Endpoints", "success": false, "message": "No auth token available", "details": null, "timestamp": "2025-08-01T01:21:45.106358"}, {"test_name": "Database Operations", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:21:47.944803"}, {"test_name": "AI Services", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:21:50.513044"}]}