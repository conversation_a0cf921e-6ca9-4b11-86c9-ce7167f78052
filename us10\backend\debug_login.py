#!/usr/bin/env python3
"""
Debug login test to see what's happening
"""

import requests
import json

BASE_URL = "http://localhost:5000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

def test_login():
    """Debug login test"""
    print("🔍 Testing login...")
    
    login_data = {
        'email': TEST_USER_EMAIL,
        'password': TEST_USER_PASSWORD
    }
    
    print(f"Login data: {login_data}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/login",
            json=login_data,
            timeout=10
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            data = response.json()
            print(f"Response data: {json.dumps(data, indent=2)}")
        except:
            print(f"Response text: {response.text}")
            
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('access_token'):
                print("✅ Login successful!")
                return True
            else:
                print(f"❌ Login failed: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == '__main__':
    test_login()
