{"timestamp": "2025-08-01T01:36:55.297668", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": true, "message": "Server responding - <PERSON><PERSON> is running", "details": null, "timestamp": "2025-08-01T01:36:09.203208"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:36:11.791855"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:14.316005"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:16.373415"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:18.413226"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:20.475969"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:22.510085"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:24.545656"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:26.590811"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:28.664856"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:30.737483"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:36:32.787453"}, {"test_name": "User Registration", "success": false, "message": "Registration validation failed: Validation failed", "details": null, "timestamp": "2025-08-01T01:36:37.474053"}, {"test_name": "User Login", "success": true, "message": "Login successful, JWT token received", "details": null, "timestamp": "2025-08-01T01:36:41.326393"}, {"test_name": "Protected API - Account Info", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:36:43.904552"}, {"test_name": "Protected API - Scan History", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:36:46.009767"}, {"test_name": "Protected API - Available Suggestions", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:36:48.066053"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 1 resumes, 1 job descriptions", "details": null, "timestamp": "2025-08-01T01:36:50.608595"}, {"test_name": "AI Services", "success": true, "message": "AI suggestion service working", "details": null, "timestamp": "2025-08-01T01:36:54.767016"}]}