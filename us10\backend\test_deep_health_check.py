#!/usr/bin/env python3
"""
Comprehensive Deep Health Check Test for Dr. Resume Application
Tests all critical components and endpoints to ensure everything is working properly
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_USER_EMAIL = "<EMAIL>"  # Use existing test user
TEST_USER_PASSWORD = "password123"

class HealthCheckTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message, details=None):
        """Log test results"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name}: {message}")
        if details and not success:
            print(f"    Details: {details}")
    
    def test_basic_connectivity(self):
        """Test basic server connectivity"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_test("Basic Connectivity", True, f"Server responding - {data.get('message', 'OK')}")
                return True
            else:
                self.log_test("Basic Connectivity", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Basic Connectivity", False, f"Connection failed: {str(e)}")
            return False
    
    def test_api_health(self):
        """Test API health endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_test("API Health", True, f"API healthy - {data.get('message', 'OK')}")
                return True
            else:
                self.log_test("API Health", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API Health", False, f"API health check failed: {str(e)}")
            return False
    
    def test_frontend_pages(self):
        """Test frontend page accessibility"""
        pages = [
            ('/', 'Landing Page'),
            ('/register', 'Registration Page'),
            ('/login', 'Login Page'),
            ('/dashboard', 'Dashboard Page'),
            ('/upload', 'Upload Page'),
            ('/job-descriptions', 'Job Descriptions Page'),
            ('/keywords', 'Keywords Page'),
            ('/matching', 'Matching Page'),
            ('/suggestions', 'Suggestions Page'),
            ('/account', 'Account Page')
        ]
        
        all_passed = True
        for path, name in pages:
            try:
                response = self.session.get(f"{self.base_url}{path}", timeout=10)
                if response.status_code == 200:
                    self.log_test(f"Frontend - {name}", True, "Page loads successfully")
                else:
                    self.log_test(f"Frontend - {name}", False, f"HTTP {response.status_code}")
                    all_passed = False
            except Exception as e:
                self.log_test(f"Frontend - {name}", False, f"Failed to load: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def test_user_registration(self):
        """Test user registration functionality"""
        try:
            # Try to register a new test user (different from existing one)
            registration_data = {
                'first_name': 'Health',
                'last_name': 'Check',
                'email': '<EMAIL>',  # Different email
                'password': 'HealthCheck123!',
                'confirm_password': 'HealthCheck123!'
            }

            response = self.session.post(
                f"{self.base_url}/api/register",
                json=registration_data,
                timeout=10
            )

            if response.status_code in [200, 201]:
                data = response.json()
                if data.get('success'):
                    self.log_test("User Registration", True, "Registration successful")
                    return True
                else:
                    self.log_test("User Registration", False, data.get('message', 'Unknown error'))
                    return False
            elif response.status_code == 400:
                # Check if it's because user already exists
                try:
                    data = response.json()
                    message = data.get('message', '').lower()
                    if 'already exists' in message or 'email already' in message or 'user already' in message:
                        self.log_test("User Registration", True, "User already exists (registration endpoint working)")
                        return True
                    else:
                        # Try a different approach - test with a unique email
                        import time
                        unique_email = f"healthcheck_{int(time.time())}@drresume.com"
                        registration_data['email'] = unique_email

                        response2 = self.session.post(
                            f"{self.base_url}/api/register",
                            json=registration_data,
                            timeout=10
                        )

                        if response2.status_code in [200, 201]:
                            data2 = response2.json()
                            if data2.get('success'):
                                self.log_test("User Registration", True, "Registration successful with unique email")
                                return True

                        self.log_test("User Registration", False, f"Registration validation failed: {data.get('message', 'Bad request')}")
                        return False
                except:
                    self.log_test("User Registration", False, "HTTP 400 - Bad request")
                    return False
            else:
                self.log_test("User Registration", False, f"HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_test("User Registration", False, f"Registration failed: {str(e)}")
            return False
    
    def test_user_login(self):
        """Test user login and JWT token generation"""
        try:
            login_data = {
                'email': TEST_USER_EMAIL,
                'password': TEST_USER_PASSWORD
            }
            
            response = self.session.post(
                f"{self.base_url}/api/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                # Check for access token in the tokens object
                tokens = data.get('tokens', {})
                access_token = tokens.get('access_token')

                if data.get('success') and access_token:
                    self.auth_token = access_token
                    self.session.headers.update({'Authorization': f'Bearer {self.auth_token}'})
                    self.log_test("User Login", True, "Login successful, JWT token received")
                    return True
                else:
                    self.log_test("User Login", False, data.get('message', 'Login failed - no token'))
                    return False
            else:
                self.log_test("User Login", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("User Login", False, f"Login failed: {str(e)}")
            return False
    
    def test_protected_endpoints(self):
        """Test protected API endpoints"""
        if not self.auth_token:
            self.log_test("Protected Endpoints", False, "No auth token available")
            return False
        
        endpoints = [
            ('/api/account', 'Account Info'),
            ('/api/scan_history', 'Scan History'),
            ('/api/available_suggestions', 'Available Suggestions')
        ]
        
        all_passed = True
        for path, name in endpoints:
            try:
                response = self.session.get(f"{self.base_url}{path}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        self.log_test(f"Protected API - {name}", True, "Endpoint accessible")
                    else:
                        self.log_test(f"Protected API - {name}", False, data.get('message', 'Unknown error'))
                        all_passed = False
                else:
                    self.log_test(f"Protected API - {name}", False, f"HTTP {response.status_code}")
                    all_passed = False
            except Exception as e:
                self.log_test(f"Protected API - {name}", False, f"Failed: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def test_database_operations(self):
        """Test database connectivity and operations"""
        try:
            # Test data availability endpoint (no auth required)
            response = self.session.get(f"{self.base_url}/api/test-available-data", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("Database Operations", True, 
                                f"Database accessible - {data.get('total_resumes', 0)} resumes, "
                                f"{data.get('total_job_descriptions', 0)} job descriptions")
                    return True
                else:
                    self.log_test("Database Operations", False, data.get('message', 'Database error'))
                    return False
            else:
                self.log_test("Database Operations", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Database Operations", False, f"Database test failed: {str(e)}")
            return False
    
    def test_ai_services(self):
        """Test AI and suggestion services"""
        try:
            # Test basic suggestions endpoint (no auth required)
            response = self.session.get(f"{self.base_url}/api/test-suggestions", timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test("AI Services", True, "AI suggestion service working")
                    return True
                else:
                    self.log_test("AI Services", False, data.get('message', 'AI service error'))
                    return False
            else:
                self.log_test("AI Services", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("AI Services", False, f"AI service test failed: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all health check tests"""
        print("🩺" + "="*60 + "🩺")
        print("🔍 Dr. Resume Deep Health Check Starting...")
        print("="*62)
        print()
        
        # Run tests in order
        tests = [
            self.test_basic_connectivity,
            self.test_api_health,
            self.test_frontend_pages,
            self.test_user_registration,
            self.test_user_login,
            self.test_protected_endpoints,
            self.test_database_operations,
            self.test_ai_services
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # Small delay between tests
        
        # Summary
        print()
        print("="*62)
        print("📊 HEALTH CHECK SUMMARY")
        print("="*62)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print()
        if failed_tests == 0:
            print("🎉 ALL TESTS PASSED! Dr. Resume is healthy and ready to use!")
        else:
            print("⚠️  Some tests failed. Please check the issues above.")
        
        print("🩺" + "="*60 + "🩺")
        
        return failed_tests == 0

def main():
    """Main function to run health checks"""
    tester = HealthCheckTester()
    success = tester.run_all_tests()
    
    # Save detailed results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"health_check_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'overall_success': success,
            'test_results': tester.test_results
        }, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
