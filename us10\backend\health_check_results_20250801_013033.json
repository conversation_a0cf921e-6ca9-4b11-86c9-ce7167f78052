{"timestamp": "2025-08-01T01:30:33.900573", "overall_success": false, "test_results": [{"test_name": "Basic Connectivity", "success": false, "message": "Connection failed: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=10)", "details": null, "timestamp": "2025-08-01T01:29:50.695726"}, {"test_name": "API Health", "success": true, "message": "API healthy - Dr. Resume API is running", "details": null, "timestamp": "2025-08-01T01:29:53.344043"}, {"test_name": "Frontend - Landing Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:29:56.216724"}, {"test_name": "Frontend - Registration Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:29:58.275206"}, {"test_name": "Frontend - <PERSON><PERSON>", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:00.344511"}, {"test_name": "Frontend - Dashboard Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:02.679028"}, {"test_name": "Frontend - Upload Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:04.753107"}, {"test_name": "Frontend - Job Descriptions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:06.943993"}, {"test_name": "Frontend - Keywords Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:09.014500"}, {"test_name": "Frontend - Matching Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:11.077693"}, {"test_name": "Frontend - Suggestions Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:13.155235"}, {"test_name": "Frontend - Account Page", "success": true, "message": "Page loads successfully", "details": null, "timestamp": "2025-08-01T01:30:15.295652"}, {"test_name": "User Registration", "success": false, "message": "Validation failed", "details": null, "timestamp": "2025-08-01T01:30:17.952659"}, {"test_name": "User Login", "success": true, "message": "Login successful, JWT token received", "details": null, "timestamp": "2025-08-01T01:30:21.509943"}, {"test_name": "Protected API - Account Info", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:30:24.089108"}, {"test_name": "Protected API - Scan History", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:30:26.161649"}, {"test_name": "Protected API - Available Suggestions", "success": true, "message": "Endpoint accessible", "details": null, "timestamp": "2025-08-01T01:30:28.235237"}, {"test_name": "Database Operations", "success": true, "message": "Database accessible - 0 resumes, 0 job descriptions", "details": null, "timestamp": "2025-08-01T01:30:30.816321"}, {"test_name": "AI Services", "success": false, "message": "HTTP 500", "details": null, "timestamp": "2025-08-01T01:30:33.391207"}]}